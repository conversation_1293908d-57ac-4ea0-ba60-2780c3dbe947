# PowerShell script for deploying RMS OP Job to corporate cluster (it-delivery namespace)

param(
    [string]$ImageRegistry = "",
    [string]$ImageTag = "latest",
    [string]$StorageClass = "",
    [switch]$DryRun = $false
)

Write-Host "🚀 RMS OP Job Corporate Deployment Script" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green

# Function to check prerequisites
function Test-Prerequisites {
    Write-Host "🔍 Checking prerequisites..." -ForegroundColor Yellow
    
    # Check kubectl
    if (!(Get-Command kubectl -ErrorAction SilentlyContinue)) {
        Write-Host "❌ kubectl is not installed or not in PATH" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ kubectl is available" -ForegroundColor Green
    
    # Check cluster connection
    try {
        kubectl cluster-info | Out-Null
        Write-Host "✅ Connected to Kubernetes cluster" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Cannot connect to Kubernetes cluster" -ForegroundColor Red
        Write-Host "Please ensure you're connected to the corporate cluster" -ForegroundColor Red
        exit 1
    }
    
    # Check namespace access
    try {
        kubectl get namespace it-delivery | Out-Null
        Write-Host "✅ it-delivery namespace exists" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Cannot access it-delivery namespace" -ForegroundColor Red
        Write-Host "Please ensure you have access to the it-delivery namespace" -ForegroundColor Red
        exit 1
    }
    
    # Check permissions
    $canCreatePods = kubectl auth can-i create pods --namespace=it-delivery
    $canCreatePVC = kubectl auth can-i create pvc --namespace=it-delivery
    
    if ($canCreatePods -eq "yes" -and $canCreatePVC -eq "yes") {
        Write-Host "✅ Sufficient permissions in it-delivery namespace" -ForegroundColor Green
    }
    else {
        Write-Host "❌ Insufficient permissions in it-delivery namespace" -ForegroundColor Red
        Write-Host "Required permissions: create pods, create pvc" -ForegroundColor Red
        exit 1
    }
}

# Function to get storage classes
function Get-StorageClasses {
    Write-Host "📦 Available Storage Classes:" -ForegroundColor Yellow
    kubectl get storageclass -o custom-columns=NAME:.metadata.name,PROVISIONER:.provisioner,DEFAULT:.metadata.annotations.'storageclass\.kubernetes\.io/is-default-class'
}

# Function to update image in deployment
function Update-DeploymentImage {
    param([string]$Registry, [string]$Tag)
    
    if ($Registry -ne "") {
        $imageName = "$Registry/rms-op-job:$Tag"
        Write-Host "🔧 Updating deployment image to: $imageName" -ForegroundColor Yellow
        
        # Update deployment.yaml
        $deploymentContent = Get-Content "k8s/deployment.yaml" -Raw
        $deploymentContent = $deploymentContent -replace "your-registry/rms-op-job:latest", $imageName
        Set-Content "k8s/deployment.yaml" -Value $deploymentContent
        
        Write-Host "✅ Updated deployment image" -ForegroundColor Green
    }
}

# Function to update storage class in PVCs
function Update-StorageClass {
    param([string]$StorageClassName)
    
    if ($StorageClassName -ne "") {
        Write-Host "🔧 Updating PVCs to use storage class: $StorageClassName" -ForegroundColor Yellow
        
        $pvcContent = Get-Content "k8s/pvc-voucher-files.yaml" -Raw
        $pvcContent = $pvcContent -replace "# storageClassName: .*", "storageClassName: $StorageClassName"
        Set-Content "k8s/pvc-voucher-files.yaml" -Value $pvcContent
        
        Write-Host "✅ Updated storage class in PVCs" -ForegroundColor Green
    }
}

# Function to deploy PVCs
function Deploy-PVCs {
    Write-Host "📦 Deploying Persistent Volume Claims..." -ForegroundColor Yellow
    
    if ($DryRun) {
        kubectl apply -f k8s/pvc-voucher-files.yaml --dry-run=client
    }
    else {
        kubectl apply -f k8s/pvc-voucher-files.yaml
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ PVCs deployed successfully" -ForegroundColor Green
    }
    else {
        Write-Host "❌ Failed to deploy PVCs" -ForegroundColor Red
        exit 1
    }
}

# Function to check PVC status
function Test-PVCStatus {
    Write-Host "🔍 Checking PVC status..." -ForegroundColor Yellow
    
    $pvcStatus = kubectl get pvc -n it-delivery -o jsonpath='{.items[*].status.phase}'
    
    if ($pvcStatus -contains "Pending") {
        Write-Host "⚠️  Some PVCs are still pending. This might be normal for dynamic provisioning." -ForegroundColor Yellow
        kubectl get pvc -n it-delivery
    }
    elseif ($pvcStatus -contains "Bound") {
        Write-Host "✅ PVCs are bound and ready" -ForegroundColor Green
    }
    else {
        Write-Host "❌ PVC status check failed" -ForegroundColor Red
        kubectl describe pvc -n it-delivery
    }
}

# Function to deploy application
function Deploy-Application {
    Write-Host "🚀 Deploying application..." -ForegroundColor Yellow
    
    if ($DryRun) {
        kubectl apply -f k8s/deployment.yaml --dry-run=client
    }
    else {
        kubectl apply -f k8s/deployment.yaml
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Application deployed successfully" -ForegroundColor Green
    }
    else {
        Write-Host "❌ Failed to deploy application" -ForegroundColor Red
        exit 1
    }
}

# Function to check deployment status
function Test-DeploymentStatus {
    Write-Host "📊 Checking deployment status..." -ForegroundColor Yellow
    
    # Wait for deployment to be ready
    Write-Host "Waiting for deployment to be ready..." -ForegroundColor Yellow
    kubectl wait --for=condition=available --timeout=300s deployment/rms-op-job -n it-delivery
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Deployment is ready" -ForegroundColor Green
        
        # Show status
        Write-Host "`n📊 Current Status:" -ForegroundColor Cyan
        Write-Host "PVCs:" -ForegroundColor White
        kubectl get pvc -n it-delivery
        Write-Host "`nPods:" -ForegroundColor White
        kubectl get pods -n it-delivery -l app=rms-op-job
        Write-Host "`nServices:" -ForegroundColor White
        kubectl get svc -n it-delivery -l app=rms-op-job
    }
    else {
        Write-Host "❌ Deployment failed or timed out" -ForegroundColor Red
        Write-Host "Checking pod status..." -ForegroundColor Yellow
        kubectl describe pods -n it-delivery -l app=rms-op-job
    }
}

# Function to show next steps
function Show-NextSteps {
    Write-Host "`n🎉 Deployment completed!" -ForegroundColor Green
    Write-Host "`n📋 Next Steps:" -ForegroundColor Cyan
    Write-Host "1. Check pod logs: kubectl logs -l app=rms-op-job -n it-delivery" -ForegroundColor White
    Write-Host "2. Port forward to access: kubectl port-forward svc/rms-op-job-service 8082:8082 -n it-delivery" -ForegroundColor White
    Write-Host "3. Upload files: kubectl cp /local/file pod-name:/voucherfiles/input/ -n it-delivery" -ForegroundColor White
    Write-Host "4. Monitor: kubectl get pods -n it-delivery -w" -ForegroundColor White
}

# Main execution
try {
    Test-Prerequisites
    Get-StorageClasses
    
    # Update configurations if provided
    Update-DeploymentImage -Registry $ImageRegistry -Tag $ImageTag
    Update-StorageClass -StorageClassName $StorageClass
    
    # Deploy
    Deploy-PVCs
    Test-PVCStatus
    Deploy-Application
    Test-DeploymentStatus
    
    if (!$DryRun) {
        Show-NextSteps
    }
}
catch {
    Write-Host "❌ Deployment failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
