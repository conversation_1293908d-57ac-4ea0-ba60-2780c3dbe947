package com.example.RMSOPJOB.service;
import com.example.RMSOPJOB.Quires.CustomQueries;
import com.example.RMSOPJOB.dto.DecryptionDto.DecryptionResponse;
import com.example.RMSOPJOB.dto.Logger.LoggerPayload;
import com.example.RMSOPJOB.utils.LoggerUtil;
import com.example.RMSOPJOB.utils.MySecurity;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.util.UUID;

@Component
public class LoadRMSVouchersJob {
    private final CustomQueries repo1;
    protected final String OPPri;
    protected final String RMSPub;
    private final String pinenckey;
    private final String filepath;
    private final String backup;
    private final LoggerUtil logger;

    public LoadRMSVouchersJob(
            CustomQueries repo1,
            @Value("${OPPRI}") String OPPri,
            @Value("${RSAPUB}") String RMSPub,
            @Value("${pinenckey}") String pinenckey,
            @Value("${backup}") String backup,
            @Value("${filepath}") String filepath, LoggerUtil logger
    ) {
        this.repo1 = repo1;
        this.OPPri = OPPri;
        this.RMSPub = RMSPub;
        this.pinenckey = pinenckey;
        this.filepath = filepath;
        this.backup = backup;
        this.logger = logger;
    }

    //
    // Runs Every 10 minutes between Midnight(00:00) and 9AM(02:59)
    @Scheduled(cron = "${LoadRMSVoucher.schedule.cron}")
    public void dycrypt() throws Exception{
        String txn= UUID.randomUUID().toString();
        logger.serviceLogger("INFO",new LoggerPayload(LocalDateTime.now().toString(),
                "Checking available Files......" ,
                "Starting File Decryption and Loading Job","OP",txn));
       MySecurity security = new MySecurity(2048);

        Path dir = Paths.get(filepath);
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(dir, "*.enc")) {
            for (Path entry : stream) {
                String currentpath =entry.toAbsolutePath().toString();
                logger.serviceLogger("INFO",new LoggerPayload(LocalDateTime.now().toString(),
                        "Decrypting File name="+currentpath+" initiated",
                        "Processing File Decryption and Loading Job","OP",txn));

               DecryptionResponse decryptionResponse= security.EVDCrypto(OPPri,RMSPub,
                        currentpath,pinenckey);
               if(decryptionResponse.getCode().equals("200")){
                   logger.serviceLogger("INFO",new LoggerPayload(LocalDateTime.now().toString(),
                           "Decrption Completed Successfully for file="+currentpath,
                           "Processing File Decryption and Loading Job","OP",txn));
              Long row_affected= repo1.BatchInsertRMSVouchers(decryptionResponse.getQuery().toString());
              System.out.println("number of Rows inserted are:"+row_affected);
              String[] file=currentpath.split("\\\\");
                new File(currentpath).renameTo(new File(backup+"/Processed"+file[file.length-1]));
            }else{
                   logger.serviceLogger("ERROR",new LoggerPayload(LocalDateTime.now().toString(),
                           "Decrypting Failed for File name="+currentpath,
                           "Completing File Decryption and Loading Job","OP",txn));
               }}
        } catch (Exception e){
                logger.serviceLogger("ERROR",new LoggerPayload(LocalDateTime.now().toString(),
                        "Exception Occurred while File Decryption and Loading Job="+e.getMessage(),
                        "Completing File Decryption and Loading Job","OP",txn));

                System.out.println(e.getMessage());
            }
    }
}
