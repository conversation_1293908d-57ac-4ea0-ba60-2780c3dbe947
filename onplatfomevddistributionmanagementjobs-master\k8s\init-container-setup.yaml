# Deployment with init container to set up directory structure
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rms-op-job-with-init
  namespace: default
  labels:
    app: rms-op-job
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rms-op-job
  template:
    metadata:
      labels:
        app: rms-op-job
    spec:
      initContainers:
      - name: setup-directories
        image: busybox:1.35
        command: ['sh', '-c']
        args:
        - |
          echo "Setting up directory structure..."
          mkdir -p /voucherfiles/input/backup
          chmod 755 /voucherfiles/input
          chmod 755 /voucherfiles/input/backup
          mkdir -p /Key
          chmod 755 /Key
          echo "Directory setup complete"
          ls -la /voucherfiles/
          ls -la /voucherfiles/input/
        volumeMounts:
        - name: voucher-files-volume
          mountPath: /voucherfiles
        - name: voucher-keys-volume
          mountPath: /Key
      containers:
      - name: rms-op-job
        image: your-registry/rms-op-job:latest  # Replace with your actual image
        ports:
        - containerPort: 8082
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "oat"
        - name: JAVA_OPTS
          value: "-Xmx768m -Xms512m"
        volumeMounts:
        - name: voucher-files-volume
          mountPath: /voucherfiles
        - name: voucher-keys-volume
          mountPath: /Key
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8082
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8082
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: voucher-files-volume
        persistentVolumeClaim:
          claimName: voucher-files-pvc
      - name: voucher-keys-volume
        persistentVolumeClaim:
          claimName: voucher-keys-pvc
