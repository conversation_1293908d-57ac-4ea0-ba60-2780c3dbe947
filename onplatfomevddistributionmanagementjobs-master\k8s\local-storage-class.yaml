# Alternative 1: Local Storage Class (if you want to use local node storage)
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: local-storage
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: false
---
# Alternative 2: HostPath Storage Class (for development/testing)
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: hostpath-storage
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: Immediate
allowVolumeExpansion: true
---
# Example Local Persistent Volume (if using local storage)
apiVersion: v1
kind: PersistentVolume
metadata:
  name: voucher-files-local-pv
  labels:
    type: local
spec:
  storageClassName: local-storage
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  local:
    path: /mnt/voucher-files  # Path on the node
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/hostname
          operator: In
          values:
          - your-node-name  # Replace with actual node name
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: voucher-keys-local-pv
  labels:
    type: local
spec:
  storageClassName: local-storage
  capacity:
    storage: 1Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  local:
    path: /mnt/voucher-keys  # Path on the node
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/hostname
          operator: In
          values:
          - your-node-name  # Replace with actual node name
