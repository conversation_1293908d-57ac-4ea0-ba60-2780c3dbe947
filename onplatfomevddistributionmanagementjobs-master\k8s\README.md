# Kubernetes Storage Configuration for RMS OP Job

This directory contains Kubernetes manifests for creating Persistent Volume Claims (PVCs) and storage for the RMS OP Job application.

## Storage Options

Since you don't have NFS or VM access, here are several storage options you can use:

### Option 1: Default Storage Class (Recommended)
Use your cluster's default storage class:

```bash
# Apply PVCs with default storage class
kubectl apply -f pvc-voucher-files.yaml
```

### Option 2: HostPath Storage (Development/Testing)
Use local node storage via HostPath (not recommended for production):

```bash
# Create storage class and PVs
kubectl apply -f local-storage-class.yaml
kubectl apply -f hostpath-pv.yaml
```

### Option 3: Cloud Provider Storage
If you're using a cloud provider, you can use their storage classes:

#### AWS EBS
```yaml
storageClassName: gp2  # or gp3
```

#### Google Cloud Persistent Disk
```yaml
storageClassName: standard  # or ssd
```

#### Azure Disk
```yaml
storageClassName: default  # or managed-premium
```

## Deployment Steps

1. **Check available storage classes:**
   ```bash
   kubectl get storageclass
   ```

2. **Create PVCs:**
   ```bash
   # For default storage class
   kubectl apply -f pvc-voucher-files.yaml
   
   # OR for hostpath storage
   kubectl apply -f local-storage-class.yaml
   kubectl apply -f hostpath-pv.yaml
   ```

3. **Verify PVCs:**
   ```bash
   kubectl get pvc
   kubectl describe pvc voucher-files-pvc
   ```

4. **Deploy the application:**
   ```bash
   kubectl apply -f deployment.yaml
   ```

## Volume Mounts

The application expects these paths:
- `/voucherfiles/input` - Input voucher files
- `/voucherfiles/input/backup` - Backup directory
- `/Key` - RSA keys directory

## Troubleshooting

1. **PVC stuck in Pending:**
   - Check if storage class exists: `kubectl get storageclass`
   - Check if there are available nodes: `kubectl get nodes`
   - For local storage, ensure the node has the required directories

2. **Pod can't mount volume:**
   - Check PVC status: `kubectl describe pvc <pvc-name>`
   - Check pod events: `kubectl describe pod <pod-name>`

3. **Permission issues:**
   - Ensure the application has proper file permissions
   - Consider using init containers to set permissions

## Security Considerations

- HostPath volumes should only be used for development/testing
- For production, use proper storage classes with encryption
- Consider using secrets for sensitive key files
- Implement proper RBAC for storage access
