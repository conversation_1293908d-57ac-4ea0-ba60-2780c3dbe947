#!/bin/bash

# Deployment script for RMS OP Job to corporate cluster (it-delivery namespace)

set -e

# Default values
IMAGE_REGISTRY=""
IMAGE_TAG="latest"
STORAGE_CLASS=""
DRY_RUN=false
NAMESPACE="it-delivery"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --image-registry)
            IMAGE_REGISTRY="$2"
            shift 2
            ;;
        --image-tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --storage-class)
            STORAGE_CLASS="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --image-registry REGISTRY    Corporate container registry URL"
            echo "  --image-tag TAG              Image tag (default: latest)"
            echo "  --storage-class CLASS        Storage class name"
            echo "  --dry-run                    Perform a dry run"
            echo "  --help                       Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

echo "🚀 RMS OP Job Corporate Deployment Script"
echo "=========================================="

# Function to check prerequisites
check_prerequisites() {
    echo "🔍 Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        echo "❌ kubectl is not installed or not in PATH"
        exit 1
    fi
    echo "✅ kubectl is available"
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        echo "❌ Cannot connect to Kubernetes cluster"
        echo "Please ensure you're connected to the corporate cluster"
        exit 1
    fi
    echo "✅ Connected to Kubernetes cluster"
    
    # Check namespace access
    if ! kubectl get namespace $NAMESPACE &> /dev/null; then
        echo "❌ Cannot access $NAMESPACE namespace"
        echo "Please ensure you have access to the $NAMESPACE namespace"
        exit 1
    fi
    echo "✅ $NAMESPACE namespace exists"
    
    # Check permissions
    if [[ $(kubectl auth can-i create pods --namespace=$NAMESPACE) == "yes" ]] && \
       [[ $(kubectl auth can-i create pvc --namespace=$NAMESPACE) == "yes" ]]; then
        echo "✅ Sufficient permissions in $NAMESPACE namespace"
    else
        echo "❌ Insufficient permissions in $NAMESPACE namespace"
        echo "Required permissions: create pods, create pvc"
        exit 1
    fi
}

# Function to show storage classes
show_storage_classes() {
    echo ""
    echo "📦 Available Storage Classes:"
    kubectl get storageclass -o custom-columns=NAME:.metadata.name,PROVISIONER:.provisioner,DEFAULT:.metadata.annotations.'storageclass\.kubernetes\.io/is-default-class'
}

# Function to update deployment image
update_deployment_image() {
    if [[ -n "$IMAGE_REGISTRY" ]]; then
        local image_name="$IMAGE_REGISTRY/rms-op-job:$IMAGE_TAG"
        echo "🔧 Updating deployment image to: $image_name"
        
        # Create backup
        cp k8s/deployment.yaml k8s/deployment.yaml.bak
        
        # Update image
        sed -i.tmp "s|your-registry/rms-op-job:latest|$image_name|g" k8s/deployment.yaml
        rm -f k8s/deployment.yaml.tmp
        
        echo "✅ Updated deployment image"
    fi
}

# Function to update storage class
update_storage_class() {
    if [[ -n "$STORAGE_CLASS" ]]; then
        echo "🔧 Updating PVCs to use storage class: $STORAGE_CLASS"
        
        # Create backup
        cp k8s/pvc-voucher-files.yaml k8s/pvc-voucher-files.yaml.bak
        
        # Update storage class
        sed -i.tmp "s|# storageClassName: .*|storageClassName: $STORAGE_CLASS|g" k8s/pvc-voucher-files.yaml
        rm -f k8s/pvc-voucher-files.yaml.tmp
        
        echo "✅ Updated storage class in PVCs"
    fi
}

# Function to deploy PVCs
deploy_pvcs() {
    echo "📦 Deploying Persistent Volume Claims..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        kubectl apply -f k8s/pvc-voucher-files.yaml --dry-run=client
    else
        kubectl apply -f k8s/pvc-voucher-files.yaml
    fi
    
    echo "✅ PVCs deployed successfully"
}

# Function to check PVC status
check_pvc_status() {
    echo "🔍 Checking PVC status..."
    
    # Wait a moment for PVCs to be processed
    sleep 5
    
    local pvc_status=$(kubectl get pvc -n $NAMESPACE -o jsonpath='{.items[*].status.phase}' 2>/dev/null || echo "")
    
    if [[ "$pvc_status" == *"Pending"* ]]; then
        echo "⚠️  Some PVCs are still pending. This might be normal for dynamic provisioning."
        kubectl get pvc -n $NAMESPACE
    elif [[ "$pvc_status" == *"Bound"* ]]; then
        echo "✅ PVCs are bound and ready"
    else
        echo "❌ PVC status check - showing details:"
        kubectl get pvc -n $NAMESPACE
    fi
}

# Function to deploy application
deploy_application() {
    echo "🚀 Deploying application..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        kubectl apply -f k8s/deployment.yaml --dry-run=client
    else
        kubectl apply -f k8s/deployment.yaml
    fi
    
    echo "✅ Application deployed successfully"
}

# Function to check deployment status
check_deployment_status() {
    echo "📊 Checking deployment status..."
    
    if [[ "$DRY_RUN" == "false" ]]; then
        # Wait for deployment to be ready
        echo "Waiting for deployment to be ready..."
        if kubectl wait --for=condition=available --timeout=300s deployment/rms-op-job -n $NAMESPACE; then
            echo "✅ Deployment is ready"
        else
            echo "❌ Deployment failed or timed out"
            echo "Checking pod status..."
            kubectl describe pods -n $NAMESPACE -l app=rms-op-job
            return 1
        fi
    fi
    
    # Show status
    echo ""
    echo "📊 Current Status:"
    echo "PVCs:"
    kubectl get pvc -n $NAMESPACE
    echo ""
    echo "Pods:"
    kubectl get pods -n $NAMESPACE -l app=rms-op-job
    echo ""
    echo "Services:"
    kubectl get svc -n $NAMESPACE -l app=rms-op-job
}

# Function to show next steps
show_next_steps() {
    echo ""
    echo "🎉 Deployment completed!"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Check pod logs: kubectl logs -l app=rms-op-job -n $NAMESPACE"
    echo "2. Port forward to access: kubectl port-forward svc/rms-op-job-service 8082:8082 -n $NAMESPACE"
    echo "3. Upload files: kubectl cp /local/file pod-name:/voucherfiles/input/ -n $NAMESPACE"
    echo "4. Monitor: kubectl get pods -n $NAMESPACE -w"
    echo ""
    echo "To get pod name for file upload:"
    echo "POD_NAME=\$(kubectl get pods -n $NAMESPACE -l app=rms-op-job -o jsonpath='{.items[0].metadata.name}')"
}

# Function to restore backups on error
cleanup_on_error() {
    echo "🧹 Cleaning up on error..."
    if [[ -f k8s/deployment.yaml.bak ]]; then
        mv k8s/deployment.yaml.bak k8s/deployment.yaml
    fi
    if [[ -f k8s/pvc-voucher-files.yaml.bak ]]; then
        mv k8s/pvc-voucher-files.yaml.bak k8s/pvc-voucher-files.yaml
    fi
}

# Trap to cleanup on error
trap cleanup_on_error ERR

# Main execution
main() {
    check_prerequisites
    show_storage_classes
    
    # Update configurations if provided
    update_deployment_image
    update_storage_class
    
    # Deploy
    deploy_pvcs
    check_pvc_status
    deploy_application
    check_deployment_status
    
    if [[ "$DRY_RUN" == "false" ]]; then
        show_next_steps
    fi
    
    # Clean up backup files
    rm -f k8s/deployment.yaml.bak k8s/pvc-voucher-files.yaml.bak
}

# Run main function
main
