apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: voucher-files-pvc
  namespace: it-delivery
  labels:
    app: rms-op-job
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  # Use default storage class - will be determined by cluster configuration
  # storageClassName: ""  # Uncomment to specify a particular storage class
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: voucher-keys-pvc
  namespace: it-delivery
  labels:
    app: rms-op-job
    component: keys
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
  # Use default storage class - will be determined by cluster configuration
  # storageClassName: ""  # Uncomment to specify a particular storage class
