apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: voucher-files-pvc
  namespace: default
  labels:
    app: rms-op-job
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  # Use default storage class - adjust based on your cluster
  # storageClassName: ""  # Uncomment to use default storage class
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: voucher-keys-pvc
  namespace: default
  labels:
    app: rms-op-job
    component: keys
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
  # Use default storage class - adjust based on your cluster
  # storageClassName: ""  # Uncomment to use default storage class
