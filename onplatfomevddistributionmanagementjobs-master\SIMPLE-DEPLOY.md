# Corporate Format Deployment Guide

## Files Created Following Your Format:

1. **`Dockerfile`** - Following your eclipse-temurin:17-jre-alpine format
2. **`k8s-simple.yaml`** - Following your deployment pattern with PVC
3. **`k8s-with-httpproxy.yaml`** - Complete with HTTPProxy for external access
4. **`Jenkinsfile`** - CI/CD pipeline following your Jenkins pattern

## Step 1: Manual Build & Deploy

```bash
# Build the JAR first
./mvnw clean package -DskipTests

# Build Docker image (following your format)
docker build -t et02-harbor.safaricomet.net/one_platform_it_delivery/one-platform-evd-distribution-management-jobs:0 .

# Push to Harbor registry
docker push et02-harbor.safaricomet.net/one_platform_it_delivery/one-platform-evd-distribution-management-jobs:0
```

## Step 2: Deploy to it-delivery namespace

```bash
# Deploy with PVC
kubectl apply -f k8s-simple.yaml

# OR deploy with HTTPProxy for external access
kubectl apply -f k8s-with-httpproxy.yaml
```

## Step 3: Copy Keys to PVC

```bash
# Get pod name
POD_NAME=$(kubectl get pods -n it-delivery -l app=one-platform-evd-distribution-management-jobs -o jsonpath='{.items[0].metadata.name}')

# Copy your RSA keys
kubectl cp /path/to/rms2048pub.key $POD_NAME:/Key/ -n it-delivery
kubectl cp /path/to/Ellamstest_private_key.key $POD_NAME:/Key/ -n it-delivery

# Copy voucher files (when needed)
kubectl cp /path/to/voucher-file.txt $POD_NAME:/voucherfiles/input/ -n it-delivery
```

## Step 4: Check Status

```bash
# Check pods
kubectl get pods -n it-delivery -l app=one-platform-evd-distribution-management-jobs

# Check logs
kubectl logs -l app=one-platform-evd-distribution-management-jobs -n it-delivery

# Check service
kubectl get svc -n it-delivery -l app=one-platform-evd-distribution-management-jobs
```

## Step 5: Access Application

### Internal Access:
```bash
kubectl port-forward svc/one-platform-evd-distribution-management-jobs-service 8082:8082 -n it-delivery
```

### External Access (if using HTTPProxy):
```
https://one-platform-evd-distribution-management-jobs.oat.sma2.safaricomet.net
```

## Jenkins CI/CD Pipeline

The `Jenkinsfile` follows your pattern and will:
1. Build JAR with Maven
2. Build Docker image with your naming convention
3. Push to Harbor registry
4. Deploy to it-delivery namespace
5. Verify deployment

## Key Features:

- ✅ Uses your Harbor registry: `et02-harbor.safaricomet.net`
- ✅ Uses your image pull secret: `harbor-registry-key`
- ✅ Follows your naming convention
- ✅ Uses it-delivery namespace
- ✅ Includes PVC for persistent storage
- ✅ Port 8082 (as per your application.properties)
- ✅ HTTPProxy with your TLS secret pattern
