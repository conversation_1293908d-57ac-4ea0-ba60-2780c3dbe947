# Simple Deployment Guide

## Step 1: Build Docker Image

```bash
# Build the JAR first
./mvnw clean package -DskipTests

# Build Docker image
docker build -t your-registry/rms-op-job:latest .

# Push to your corporate registry
docker push your-registry/rms-op-job:latest
```

## Step 2: Update Image in Manifest

Edit `k8s-simple.yaml` and replace:
```yaml
image: your-registry/rms-op-job:latest
```

## Step 3: Deploy to Cluster

```bash
kubectl apply -f k8s-simple.yaml
```

## Step 4: Copy Keys to PVC

```bash
# Get pod name
POD_NAME=$(kubectl get pods -n it-delivery -l app=rms-op-job -o jsonpath='{.items[0].metadata.name}')

# Copy your RSA keys
kubectl cp /path/to/rms2048pub.key $POD_NAME:/Key/ -n it-delivery
kubectl cp /path/to/Ellamstest_private_key.key $POD_NAME:/Key/ -n it-delivery

# Copy voucher files (when needed)
kubectl cp /path/to/voucher-file.txt $POD_NAME:/voucherfiles/input/ -n it-delivery
```

## Step 5: Check Status

```bash
# Check pods
kubectl get pods -n it-delivery

# Check logs
kubectl logs -l app=rms-op-job -n it-delivery

# Port forward to test
kubectl port-forward svc/rms-op-job-service 8082:8082 -n it-delivery
```

## That's it!

The PVC will persist your keys and files even if the pod restarts.
