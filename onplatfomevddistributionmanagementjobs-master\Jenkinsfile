pipeline {
    agent any
    
    environment {
        HARBOR_REGISTRY = 'et02-harbor.safaricomet.net'
        PROJECT_NAME = 'one_platform_it_delivery'
        IMAGE_NAME = 'one-platform-evd-distribution-management-jobs'
        NAMESPACE = 'it-delivery'
        HARBOR_CREDENTIALS = 'harbor-registry-key'
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }
        
        stage('Build JAR') {
            steps {
                script {
                    sh './mvnw clean package -DskipTests'
                }
            }
        }
        
        stage('Build Docker Image') {
            steps {
                script {
                    def imageTag = "${BUILD_NUMBER}"
                    def fullImageName = "${HARBOR_REGISTRY}/${PROJECT_NAME}/${IMAGE_NAME}:${imageTag}"
                    
                    sh "docker build -t ${fullImageName} ."
                    
                    // Also tag as latest
                    sh "docker tag ${fullImageName} ${HARBOR_REGISTRY}/${PROJECT_NAME}/${IMAGE_NAME}:latest"
                    
                    env.IMAGE_TAG = imageTag
                    env.FULL_IMAGE_NAME = fullImageName
                }
            }
        }
        
        stage('Push to Harbor') {
            steps {
                script {
                    withCredentials([usernamePassword(credentialsId: "${HARBOR_CREDENTIALS}", 
                                                   usernameVariable: 'HARBOR_USER', 
                                                   passwordVariable: 'HARBOR_PASS')]) {
                        sh "echo ${HARBOR_PASS} | docker login ${HARBOR_REGISTRY} -u ${HARBOR_USER} --password-stdin"
                        sh "docker push ${FULL_IMAGE_NAME}"
                        sh "docker push ${HARBOR_REGISTRY}/${PROJECT_NAME}/${IMAGE_NAME}:latest"
                    }
                }
            }
        }
        
        stage('Update Deployment') {
            steps {
                script {
                    // Update the image tag in the deployment file
                    sh """
                        sed -i 's|${HARBOR_REGISTRY}/${PROJECT_NAME}/${IMAGE_NAME}:.*|${HARBOR_REGISTRY}/${PROJECT_NAME}/${IMAGE_NAME}:${IMAGE_TAG}|g' k8s-simple.yaml
                    """
                }
            }
        }
        
        stage('Deploy to Kubernetes') {
            steps {
                script {
                    sh "kubectl apply -f k8s-simple.yaml"
                    
                    // Wait for deployment to be ready
                    sh "kubectl rollout status deployment/${IMAGE_NAME} -n ${NAMESPACE} --timeout=300s"
                }
            }
        }
        
        stage('Verify Deployment') {
            steps {
                script {
                    sh "kubectl get pods -n ${NAMESPACE} -l app=${IMAGE_NAME}"
                    sh "kubectl get svc -n ${NAMESPACE} -l app=${IMAGE_NAME}"
                }
            }
        }
    }
    
    post {
        always {
            // Clean up local docker images
            sh "docker rmi ${FULL_IMAGE_NAME} || true"
            sh "docker rmi ${HARBOR_REGISTRY}/${PROJECT_NAME}/${IMAGE_NAME}:latest || true"
        }
        success {
            echo "Deployment successful!"
            script {
                def podName = sh(
                    script: "kubectl get pods -n ${NAMESPACE} -l app=${IMAGE_NAME} -o jsonpath='{.items[0].metadata.name}'",
                    returnStdout: true
                ).trim()
                
                echo "Pod name: ${podName}"
                echo "To copy files: kubectl cp /local/file ${podName}:/voucherfiles/input/ -n ${NAMESPACE}"
                echo "To copy keys: kubectl cp /local/key ${podName}:/Key/ -n ${NAMESPACE}"
            }
        }
        failure {
            echo "Deployment failed!"
            sh "kubectl describe pods -n ${NAMESPACE} -l app=${IMAGE_NAME}"
        }
    }
}
