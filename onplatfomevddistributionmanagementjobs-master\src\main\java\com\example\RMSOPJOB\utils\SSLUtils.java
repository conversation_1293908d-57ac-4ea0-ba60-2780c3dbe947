package com.example.RMSOPJOB.utils;

import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import reactor.netty.http.client.HttpClient;

import javax.net.ssl.TrustManagerFactory;
import java.io.FileInputStream;
import java.security.KeyStore;

public class SSLUtils {
    public static HttpClient createCustomSSLClient(String trustStorePath, String trustStorePassword) {


        try {
            // Load your custom truststore (JKS or PKCS12)
            KeyStore trustStore = KeyStore.getInstance("JKS");
            try (FileInputStream trustStoreStream = new FileInputStream(trustStorePath)) {
                trustStore.load(trustStoreStream, trustStorePassword.toCharArray());
            }

            // Initialize TrustManagerFactory with the custom truststore
            TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(
                    TrustManagerFactory.getDefaultAlgorithm());
            trustManagerFactory.init(trustStore);

            // Build Netty SSL context with the custom trust managers
            SslContext sslContext = SslContextBuilder.forClient()
                    .trustManager(trustManagerFactory)
                    .build();

            // Create HttpClient with custom SSL context
            return HttpClient.create()
                    .secure(spec -> spec.sslContext(sslContext));

        } catch (Exception e) {
            throw new RuntimeException("Failed to create custom SSL HttpClient", e);
        }
    }
}