# Corporate Cluster Deployment Guide - IT-Delivery Namespace

This guide helps you deploy the RMS OP Job application to your corporate Kubernetes cluster in the `it-delivery` namespace.

## Prerequisites

1. **Cluster Access**: Ensure you have access to the corporate Kubernetes cluster
2. **Namespace Access**: Verify you have permissions to deploy to the `it-delivery` namespace
3. **kubectl**: Ensure kubectl is configured to connect to your corporate cluster
4. **Container Image**: Your application image should be available in your corporate registry

## Pre-Deployment Checks

### 1. Verify Cluster Connection
```bash
kubectl cluster-info
kubectl get nodes
```

### 2. Check Namespace Access
```bash
kubectl get namespaces | grep it-delivery
kubectl auth can-i create pods --namespace=it-delivery
kubectl auth can-i create pvc --namespace=it-delivery
```

### 3. Check Available Storage Classes
```bash
kubectl get storageclass
```

### 4. Verify Corporate Registry Access
```bash
# Check if you can pull images from your corporate registry
kubectl get secrets -n it-delivery | grep registry
```

## Deployment Steps

### Step 1: Create/Verify Namespace
```bash
# Check if namespace exists
kubectl get namespace it-delivery

# If it doesn't exist, create it (if you have permissions)
kubectl create namespace it-delivery
```

### Step 2: Configure Storage Class (if needed)
If your corporate cluster uses a specific storage class, update the PVC files:

```bash
# Check what storage classes are available
kubectl get storageclass

# Edit PVC files to specify the correct storage class
# Uncomment and set the storageClassName in pvc-voucher-files.yaml
```

### Step 3: Update Container Image
Edit `k8s/deployment.yaml` and replace `your-registry/rms-op-job:latest` with your actual corporate registry image:

```yaml
image: your-corporate-registry.com/it-delivery/rms-op-job:v1.0.0
```

### Step 4: Deploy PVCs
```bash
kubectl apply -f k8s/pvc-voucher-files.yaml
```

### Step 5: Verify PVCs
```bash
kubectl get pvc -n it-delivery
kubectl describe pvc voucher-files-pvc -n it-delivery
kubectl describe pvc voucher-keys-pvc -n it-delivery
```

### Step 6: Deploy Application
```bash
kubectl apply -f k8s/deployment.yaml
```

### Step 7: Monitor Deployment
```bash
# Check deployment status
kubectl get deployments -n it-delivery
kubectl get pods -n it-delivery

# Check pod logs
kubectl logs -l app=rms-op-job -n it-delivery

# Describe pod for troubleshooting
kubectl describe pod -l app=rms-op-job -n it-delivery
```

## Corporate-Specific Configurations

### Image Pull Secrets
If your corporate registry requires authentication:

```yaml
# Add to deployment.yaml under spec.template.spec:
imagePullSecrets:
- name: corporate-registry-secret
```

### Resource Quotas
Check if the namespace has resource quotas:

```bash
kubectl describe quota -n it-delivery
kubectl describe limitrange -n it-delivery
```

### Network Policies
Verify network policies allow your application to communicate:

```bash
kubectl get networkpolicy -n it-delivery
```

### Security Context
Add security context if required by corporate policies:

```yaml
# Add to container spec in deployment.yaml:
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - ALL
```

## Troubleshooting

### PVC Issues
```bash
# Check storage class
kubectl get storageclass

# Check PVC events
kubectl describe pvc voucher-files-pvc -n it-delivery

# Check if storage provisioner is working
kubectl get events -n it-delivery --sort-by='.lastTimestamp'
```

### Pod Issues
```bash
# Check pod status
kubectl get pods -n it-delivery -o wide

# Check pod logs
kubectl logs -l app=rms-op-job -n it-delivery --previous

# Check pod events
kubectl describe pod -l app=rms-op-job -n it-delivery
```

### Image Pull Issues
```bash
# Check if image exists and is accessible
kubectl run test-image --image=your-registry/rms-op-job:latest --dry-run=client -o yaml

# Check image pull secrets
kubectl get secrets -n it-delivery
```

## File Upload to PVC

Once deployed, you can upload files to the persistent volume:

```bash
# Get pod name
POD_NAME=$(kubectl get pods -n it-delivery -l app=rms-op-job -o jsonpath='{.items[0].metadata.name}')

# Copy files to the pod
kubectl cp /local/path/to/voucher-file.txt $POD_NAME:/voucherfiles/input/ -n it-delivery

# Copy RSA keys
kubectl cp /local/path/to/rms2048pub.key $POD_NAME:/Key/ -n it-delivery
kubectl cp /local/path/to/private_key.key $POD_NAME:/Key/ -n it-delivery
```

## Monitoring and Maintenance

### Check Application Health
```bash
# Port forward to access the application
kubectl port-forward svc/rms-op-job-service 8082:8082 -n it-delivery

# Access health endpoint
curl http://localhost:8082/actuator/health
```

### View Application Logs
```bash
# Follow logs
kubectl logs -f -l app=rms-op-job -n it-delivery

# View logs from specific time
kubectl logs --since=1h -l app=rms-op-job -n it-delivery
```

### Scale Application
```bash
# Scale up/down
kubectl scale deployment rms-op-job --replicas=2 -n it-delivery
```

## Cleanup

To remove the deployment:

```bash
kubectl delete -f k8s/deployment.yaml
kubectl delete -f k8s/pvc-voucher-files.yaml
```

**Note**: PVCs and their data will be retained unless explicitly deleted.
