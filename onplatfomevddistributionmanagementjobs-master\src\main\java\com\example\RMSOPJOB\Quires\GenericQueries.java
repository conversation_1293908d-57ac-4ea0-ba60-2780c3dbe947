package com.example.RMSOPJOB.Quires;

import com.example.RMSOPJOB.config.DBconfig;
import com.example.RMSOPJOB.dto.Logger.LoggerPayload;
import com.example.RMSOPJOB.utils.LoggerUtil;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class GenericQueries {
    private final LoggerUtil logger;
    private final JdbcTemplate jdbcTemplate;
    private final DBconfig dBconfig;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;


    public GenericQueries(LoggerUtil logger, DBconfig dBconfig) throws Exception {
        this.logger = logger;
        this.jdbcTemplate = new JdbcTemplate(dBconfig.poolmanagement());
        this.namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dBconfig.poolmanagement());
        this.dBconfig = dBconfig;
    }

    public  <T> List<T> selectExecutor(String Query,
                                       String className, Boolean has_params, Map<String, Object> parammap,
                                       String txn, String serviceName) throws Exception {
        try {
            @SuppressWarnings("unchecked")
            Class<T> Object =(Class<T>) Class.forName(className);
            if (has_params) {
                return namedParameterJdbcTemplate.query(Query,parammap,new BeanPropertyRowMapper<>(Object));

            } else {
                return jdbcTemplate.query(Query, new BeanPropertyRowMapper<>(Object));

            }
        } catch (Exception e) {


            logger.serviceLogger("ERROR",new LoggerPayload(LocalDateTime.now().toString(),
                    "Exception During JDBC Query;"+e.getMessage(),
                    "Progressing "+serviceName+" request", txn, "OP"));
            e.printStackTrace();
            return null;
        }

    }


    public int upsertDeleteExecutor(String Query,Boolean has_params,Map<String, Object> parammap,
                                    String txn,String serviceName) throws SQLException {
        try {
            if (has_params) {

                return namedParameterJdbcTemplate.update(Query, parammap);
            }

            else {
                return jdbcTemplate.update(Query);
            }
        } catch (Exception e) {
            logger.serviceLogger("ERROR",new LoggerPayload(LocalDateTime.now().toString(),
                    "Exception During JDBC Query;"+e.getMessage(),
                    "Progressing "+serviceName+" request",txn, "OP"));
            e.printStackTrace();
            return 0;
        }


    }
    public List<String> stringresponse(String Query,
                                       Boolean has_params
            ,Map<String, Object> parammap, String txn,String serviceName){
        try {
            if (has_params) {
                return namedParameterJdbcTemplate.queryForList(
                        Query,
                        parammap,
                        String.class
                );
            } else {
                return namedParameterJdbcTemplate.queryForList(
                        Query,
                        Collections.emptyMap(),  // empty params map
                        String.class
                );

            }


        }catch(Exception e) {
e.printStackTrace();
            logger.serviceLogger("ERROR",new LoggerPayload(LocalDateTime.now().toString(),
                    "Exception During JDBC Query;"+e.getMessage(),
                    "Progressing "+serviceName+" request", txn, "OP"));
            return null;
        }
    }
    public String Customquery(String sql,Map<String, Object> parammap,String txn,String serviceName){
        try {
            return namedParameterJdbcTemplate.queryForObject(sql, parammap, String.class);

        }catch (Exception e){
            logger.serviceLogger("ERROR",new LoggerPayload(LocalDateTime.now().toString(),
                    "Exception During JDBC Query;"+e.getMessage(),
                    "Progressing "+serviceName+" request",txn,"OP"));
            e.printStackTrace();
            return null;

        }
    }

}