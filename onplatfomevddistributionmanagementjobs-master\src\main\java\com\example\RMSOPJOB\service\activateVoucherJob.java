package com.example.RMSOPJOB.service;

import com.example.RMSOPJOB.Quires.GenericQueries;
import com.example.RMSOPJOB.client.CallTibcoActivation;
import com.example.RMSOPJOB.dto.Logger.LoggerPayload;
import com.example.RMSOPJOB.dto.tibcoactivatecardbySN.responsepayload.ActivateCardBySNResponsePayload;
import com.example.RMSOPJOB.dto.tibcotoken.TokenResponsePayload;

import com.example.RMSOPJOB.utils.LoggerUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

@Service
public class activateVoucherJob {

    private final GenericQueries query;
    private final CallTibcoActivation callTibco;
    private final String RMSsuccsscode;
    private final LoggerUtil logger;
    private final String RMSalreadyActivecode;

    public activateVoucherJob(GenericQueries query, CallTibcoActivation callTibco,
                              @Value("${clinet.RMSsuccsscode}") String rmSsuccsscode, LoggerUtil logger,
                              @Value("${clinet.RMSalreadyActivecode}")String rmSalreadyActivecode) {
        this.query = query;
        this.callTibco = callTibco;
        RMSsuccsscode = rmSsuccsscode;
        this.logger = logger;
        RMSalreadyActivecode = rmSalreadyActivecode;
    }
    //Runs every hour
@Scheduled(cron = "${ActivateCard.schedule.cron}")
public void activateBatch() {
        System.out.println("Started Activation Job");
    String txn=UUID.randomUUID().toString();
    try {

        logger.serviceLogger("INFO",new LoggerPayload(LocalDateTime.now().toString(),
                "Querying..... bookedin Vouchers" ,
                "Starting Voucher Activation Job","OP",txn));
        List<String> rs = query.stringresponse("select Distinct batch_number from public.new_vouchers where is_active = false"
                ,false,null,txn,"Activation Job");
        if (rs.isEmpty()) {

            logger.serviceLogger("INFO",new LoggerPayload(LocalDateTime.now().toString(),
                    "No Batch to be activated",
                    "Completing Voucher Activation Job","OP",txn));
        }
        else {
        for(String batch:rs) {

            System.out.println("About to call Token");
            TokenResponsePayload tokenResponsePayload = callTibco.GenerateTibcoToken().block();

            assert tokenResponsePayload != null;
            if (tokenResponsePayload.getStatus().equals("1000")) {

                logger.serviceLogger("INFO",new LoggerPayload(LocalDateTime.now().toString(),
                        "Token Generated about to call activate voucher for batch no="+ batch,
                        "Processing Voucher Activation Job","OP",txn));

                ActivateCardBySNResponsePayload activateCardBySNResponsePayload = (Objects.requireNonNull(callTibco.
                        activatecardbysn(batch, tokenResponsePayload.getToken()).block()));

                if (activateCardBySNResponsePayload.
                        getVoucherActivationVBMResponse().
                        getVoucherActivationVBO().
                        getStatusCode()
                        .equals(RMSsuccsscode)) {
                    logger.serviceLogger("INFO",new LoggerPayload(LocalDateTime.now().toString(),
                            "Activation ResponsePayload"+ activateCardBySNResponsePayload,
                            "Processing Voucher Activation Job","OP",txn));
                    System.out.println("about to Update");


                 Map<String,Object> param=new HashMap<>();
                 param.put("batch_number",batch);

                 int count= query.upsertDeleteExecutor("update  public.vouchermanagment_tbl set is_active = true ,status=1 " +
                         "where batch_number =:batch_number and status =0;",true,param,txn,"Voucher Activation Job");

                    logger.serviceLogger("INFO",new LoggerPayload(LocalDateTime.now().toString(),
                            batch+" has been activated, number of rows affected is "+count,
                            "Processing Voucher Activation Job","OP",txn));

                    System.out.println(batch+" has been activated, number of rows affected is "+count
                 );
                } else {
                    logger.serviceLogger("ERROR",new LoggerPayload(LocalDateTime.now().toString(),
                            "Activation issue:::"+activateCardBySNResponsePayload,
                            "Completing Voucher Activation Job","OP",txn));
                    System.out.println("Activation issue:::"+activateCardBySNResponsePayload);
                }
            } else {
                logger.serviceLogger("ERROR",new LoggerPayload(LocalDateTime.now().toString(),
                        "Token Error issue:::"+ tokenResponsePayload,
                        "Completing Voucher Activation Job","OP",txn));
                System.out.println("Token Error");
            }
        }
        }

    } catch (Exception e) {
        logger.serviceLogger("ERROR",new LoggerPayload(LocalDateTime.now().toString(),
                "Exception Occuered while Processing Activating Job "+e.getMessage(),
                "Completing Voucher Activation Job","OP",txn));

        System.out.println(e.getMessage());
    }
        System.out.println("Job finished");
    }
}
