package com.example.RMSOPJOB.utils;

import com.example.RMSOPJOB.dto.Logger.LoggerPayload;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Locale;

@Slf4j
@Component
public class LoggerUtil {

    public void serviceLogger(String loglevel, LoggerPayload loggerPayload){

        try {

            ObjectMapper objectMapper = new ObjectMapper();
            if (loglevel.toUpperCase(Locale.ROOT).equals("INFO")) {

                log.info(objectMapper.writeValueAsString(loggerPayload));
            }else{
                log.error(objectMapper.writeValueAsString(loggerPayload));
            }
        }catch (Exception e){
            System.out.println("error Occurred while logging");
        }
    }



}
