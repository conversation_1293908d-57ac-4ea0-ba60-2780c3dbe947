apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: evd-distribution-management-jobs-storage
  namespace: it-delivery
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: it-delivery
  name: one-platform-evd-distribution-management-jobs
  labels:
    app: one-platform-evd-distribution-management-jobs
spec:
  replicas: 1
  selector:
    matchLabels:
      app: one-platform-evd-distribution-management-jobs
  template:
    metadata:
      labels:
        app: one-platform-evd-distribution-management-jobs
    spec:
      imagePullSecrets:
        - name: harbor-registry-key
      containers:
        - name: one-platform-evd-distribution-management-jobs
          image: et02-harbor.safaricomet.net/one_platform_it_delivery/one-platform-evd-distribution-management-jobs:0

          # imagePullPolicy: Always
          # envFrom:
          #   - secretRef:
          #       name: common-default-secret
          #   - configMapRef:
          #       name: common-default-cm

          ports:
            - containerPort: 8082

          volumeMounts:
            - name: storage
              mountPath: /voucherfiles
              subPath: voucherfiles
            - name: storage
              mountPath: /Key
              subPath: keys

          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "1Gi"
              cpu: "500m"

      volumes:
        - name: storage
          persistentVolumeClaim:
            claimName: evd-distribution-management-jobs-storage
---
apiVersion: v1
kind: Service
metadata:
  name: one-platform-evd-distribution-management-jobs-service
  namespace: it-delivery
  labels:
    app: one-platform-evd-distribution-management-jobs
    run: one-platform-evd-distribution-management-jobs
spec:
  ports:
    - name: http
      protocol: TCP
      port: 8082
      targetPort: 8082
  selector:
    app: one-platform-evd-distribution-management-jobs
