# Simple Kubernetes deployment for it-delivery namespace
# Just copy your keys to the PVC after deployment

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: voucher-storage
  namespace: it-delivery
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rms-op-job
  namespace: it-delivery
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rms-op-job
  template:
    metadata:
      labels:
        app: rms-op-job
    spec:
      containers:
      - name: rms-op-job
        image: your-registry/rms-op-job:latest  # UPDATE THIS
        ports:
        - containerPort: 8082
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "oat"
        volumeMounts:
        - name: storage
          mountPath: /voucherfiles
          subPath: voucherfiles
        - name: storage
          mountPath: /Key
          subPath: keys
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: storage
        persistentVolumeClaim:
          claimName: voucher-storage
---
apiVersion: v1
kind: Service
metadata:
  name: rms-op-job-service
  namespace: it-delivery
spec:
  selector:
    app: rms-op-job
  ports:
  - port: 8082
    targetPort: 8082
  type: ClusterIP
