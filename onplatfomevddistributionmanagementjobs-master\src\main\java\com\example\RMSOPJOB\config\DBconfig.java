package com.example.RMSOPJOB.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.postgresql.PGConnection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DriverManagerDataSource;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

@Configuration
public class DBconfig {
      private final String dbUrl;
      private final String userName;
      private final String password;
      private final String driverClassName;
      private final String timeout;
    @Autowired
        public DBconfig(@Value("${JdbcUrl}") String dbUrl,
                    @Value("${setUser}") String userName,
                    @Value("${setPassword}") String password,
                    @Value("${DriverClass}")String driverClassName,
                    @Value("${setLoginTimeout}") String timeout) {
        this.dbUrl = dbUrl;
        this.userName = userName;
        this.password = password;
        this.driverClassName = driverClassName;
        this.timeout= timeout;
    }

    @Bean
public DataSource dataSource () throws SQLException {
    DriverManagerDataSource dataSource1 = new DriverManagerDataSource();
    dataSource1.setUrl(dbUrl);
    dataSource1.setUsername(userName);
    dataSource1.setPassword(password);
//    dataSource1.setLoginTimeout(Integer.parseInt(timeout));
    dataSource1.setDriverClassName(driverClassName);
    return dataSource1;
}
public PGConnection createBaseConnection() throws SQLException {
    DriverManager.setLoginTimeout(120);
    Connection conn = DriverManager.getConnection(dbUrl, userName, password);
    return conn.unwrap(PGConnection.class);
    }


    @Bean
    public DataSource poolmanagement() throws Exception{
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl("*****************************************");
        hikariConfig.setUsername("postgres");
        hikariConfig.setPassword("test");
        hikariConfig.setMaximumPoolSize(10);
        hikariConfig.setPoolName("pool");
        // Optional tuning
        hikariConfig.setConnectionTimeout(30000);  // 30 seconds
        hikariConfig.setIdleTimeout(600000);       // 10 minutes
        hikariConfig.setMaxLifetime(1800000);      // 30 minutes
        hikariConfig.setLeakDetectionThreshold(15000);  // 15 seconds
        return new HikariDataSource(hikariConfig);
    }
}
