package com.example.RMSOPJOB.dto.tibcoactivatecardbySN.requestpayload;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JacksonStdImpl;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Name_Desc {
    @JsonProperty("Name")
private String Name;
    @JsonProperty("Desc")
private String Desc;

}
