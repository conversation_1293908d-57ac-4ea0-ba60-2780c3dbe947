package com.example.RMSOPJOB.dto.tibcotoken;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TokenResponsePayload {
     @JsonProperty("Status")
     private String Status;
     @JsonProperty("Desc")
     private String Desc;
     @JsonProperty("Token")
     private String Token;
     @JsonProperty("expires_on")
     private String expires_on;
}


