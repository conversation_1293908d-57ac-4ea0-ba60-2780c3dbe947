#!/bin/bash

# Deployment script for RMS OP Job with PVC setup
# This script helps deploy the application with persistent storage

set -e

echo "🚀 RMS OP Job Deployment Script"
echo "================================"

# Function to check if kubectl is available
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        echo "❌ kubectl is not installed or not in PATH"
        exit 1
    fi
    echo "✅ kubectl is available"
}

# Function to check cluster connectivity
check_cluster() {
    if ! kubectl cluster-info &> /dev/null; then
        echo "❌ Cannot connect to Kubernetes cluster"
        echo "Please ensure you're connected to a cluster and have proper permissions"
        exit 1
    fi
    echo "✅ Connected to Kubernetes cluster"
}

# Function to list available storage classes
list_storage_classes() {
    echo ""
    echo "📦 Available Storage Classes:"
    kubectl get storageclass -o custom-columns=NAME:.metadata.name,PROVISIONER:.provisioner,DEFAULT:.metadata.annotations.'storageclass\.kubernetes\.io/is-default-class'
}

# Function to deploy with default storage class
deploy_default_storage() {
    echo ""
    echo "🔧 Deploying with default storage class..."
    kubectl apply -f k8s/pvc-voucher-files.yaml
    kubectl apply -f k8s/deployment.yaml
    echo "✅ Deployed with default storage class"
}

# Function to deploy with hostpath storage
deploy_hostpath_storage() {
    echo ""
    echo "🔧 Deploying with hostpath storage..."
    kubectl apply -f k8s/local-storage-class.yaml
    kubectl apply -f k8s/hostpath-pv.yaml
    
    # Update deployment to use hostpath PVCs
    sed 's/voucher-files-pvc/voucher-files-hostpath-pvc/g; s/voucher-keys-pvc/voucher-keys-hostpath-pvc/g' k8s/deployment.yaml | kubectl apply -f -
    echo "✅ Deployed with hostpath storage"
}

# Function to check deployment status
check_status() {
    echo ""
    echo "📊 Checking deployment status..."
    echo ""
    echo "PVCs:"
    kubectl get pvc -l app=rms-op-job
    echo ""
    echo "Pods:"
    kubectl get pods -l app=rms-op-job
    echo ""
    echo "Services:"
    kubectl get svc -l app=rms-op-job
}

# Function to show logs
show_logs() {
    echo ""
    echo "📋 Recent logs:"
    kubectl logs -l app=rms-op-job --tail=20 || echo "No logs available yet"
}

# Main menu
main_menu() {
    echo ""
    echo "Please choose a deployment option:"
    echo "1) Use default storage class (recommended)"
    echo "2) Use hostpath storage (development/testing only)"
    echo "3) Check current status"
    echo "4) Show application logs"
    echo "5) Exit"
    echo ""
    read -p "Enter your choice (1-5): " choice
    
    case $choice in
        1)
            deploy_default_storage
            check_status
            ;;
        2)
            deploy_hostpath_storage
            check_status
            ;;
        3)
            check_status
            ;;
        4)
            show_logs
            ;;
        5)
            echo "👋 Goodbye!"
            exit 0
            ;;
        *)
            echo "❌ Invalid choice. Please try again."
            main_menu
            ;;
    esac
}

# Main execution
main() {
    check_kubectl
    check_cluster
    list_storage_classes
    main_menu
    
    echo ""
    echo "🎉 Deployment completed!"
    echo ""
    echo "Next steps:"
    echo "1. Check pod status: kubectl get pods -l app=rms-op-job"
    echo "2. View logs: kubectl logs -l app=rms-op-job -f"
    echo "3. Access the service: kubectl port-forward svc/rms-op-job-service 8082:8082"
    echo ""
    echo "To upload files to the persistent volume:"
    echo "kubectl cp /local/path/to/file pod-name:/voucherfiles/input/"
}

# Run main function
main
