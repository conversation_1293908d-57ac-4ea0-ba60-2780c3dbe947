FROM openjdk:17-jre-slim

# Set working directory
WORKDIR /app

# Create necessary directories
RUN mkdir -p /voucherfiles/input/backup && \
    mkdir -p /Key && \
    chmod 755 /voucherfiles/input && \
    chmod 755 /voucherfiles/input/backup && \
    chmod 755 /Key

# Copy the JAR file
COPY target/*.jar app.jar

# Set environment variables
ENV SPRING_PROFILES_ACTIVE=oat
ENV JAVA_OPTS="-Xmx768m -Xms512m"

# Expose port
EXPOSE 8082

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8082/actuator/health || exit 1

# Run the application
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
