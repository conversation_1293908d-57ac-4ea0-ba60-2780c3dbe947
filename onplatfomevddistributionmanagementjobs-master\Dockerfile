FROM eclipse-temurin:17-jre-alpine

# Timezone
ENV TZ=Africa/Nairobi
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Rudementary
WORKDIR /app

# Application name same as deployment name
LABEL name="one-platform-evd-distribution-management-jobs" \
description="RMS OP Job for voucher processing and activation" \
maintainer="IT Delivery Team"
MAINTAINER "IT Delivery Team"

# Copy Truststore
COPY Truststore.jks /app/Truststore.jks

# Create necessary directories
RUN mkdir -p /voucherfiles/input/backup && \
    mkdir -p /Key && \
    chmod 755 /voucherfiles/input && \
    chmod 755 /voucherfiles/input/backup && \
    chmod 755 /Key

# Port
EXPOSE 8082

# Add App
ARG JAR_FILE=target/*.jar
COPY ${JAR_FILE} /app/service.jar

# User mode
USER root

ENTRYPOINT ["java","-jar","/app/service.jar"]
