# HostPath Persistent Volumes (for development/testing environments)
# WARNING: HostPath volumes are not suitable for production use
apiVersion: v1
kind: PersistentVolume
metadata:
  name: voucher-files-hostpath-pv
  labels:
    type: hostpath
spec:
  storageClassName: hostpath-storage
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Delete
  hostPath:
    path: /tmp/voucher-files
    type: DirectoryOrCreate
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: voucher-keys-hostpath-pv
  labels:
    type: hostpath
spec:
  storageClassName: hostpath-storage
  capacity:
    storage: 1Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Delete
  hostPath:
    path: /tmp/voucher-keys
    type: DirectoryOrCreate
---
# Updated PVCs to use hostpath storage class
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: voucher-files-hostpath-pvc
  namespace: default
  labels:
    app: rms-op-job
    component: storage
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: hostpath-storage
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: voucher-keys-hostpath-pvc
  namespace: default
  labels:
    app: rms-op-job
    component: keys
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
  storageClassName: hostpath-storage
