package com.example.RMSOPJOB.client;

import com.example.RMSOPJOB.dto.tibcoactivatecardbySN.requestpayload.*;
import com.example.RMSOPJOB.dto.tibcoactivatecardbySN.responsepayload.ActivateCardBySNResponsePayload;
import com.example.RMSOPJOB.dto.tibcotoken.TokenRequestPayload;
import com.example.RMSOPJOB.dto.tibcotoken.TokenResponsePayload;
import com.example.RMSOPJOB.utils.SSLUtils;
import com.example.RMSOPJOB.utils.SSLbypasshttpclient;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.UUID;

@Component
public class CallTibcoActivation {
    private final WebClient tokenwebClient;
    private final WebClient activatecardwebClient;
    //private final String truststorepath ;
    //protected final String truststorepassword;
    private final String tokenbaseUrl;
    private final String  TokenURI;
    private final String activateCardbaseUrl;
    private final String activationURI;
    private final String tokenUsername;
    protected final String tokenPassword;
    private  final String tokensourcesystem;
    private  final String tokensourceidentity;
    private  final String ActivationSourcesystem;
    private  final String Activaterouteid;
    //
    //
    @Autowired
    public CallTibcoActivation(
            //@Value("${clinet.Tibco.truststoreURL}") String truststorepath,
           // @Value("${clinet.Tibco.truststorepassword}") String truststorepassword,
            @Value("${clinet.Tibco.TokenBaseURI}")String tokenbaseUrl,
            @Value("${clinet.Tibco.TokenURI}") String tokenURI,
            @Value("${clinet.Tibco.activationBaseURI}")String activateCardbaseUrl,
            @Value("${clinet.Tibco.activationURI}")String activationURI,
            @Value("${clinet.Tibco.TokenUsername}") String tokenUsername,
            @Value("${clinet.Tibco.TokenPassword}") String tokenPassword,
            @Value("${client.Tibco.token.x-source-system}") String tokensourcesystem,
            @Value("${client.Tibco.token.x-source-identity-token}")String tokensourceidentity,
            @Value("${client.Tibco.activatecardbysn.x-source-system}")String activationSourcesystem,
            @Value("${client.Tibco.activatecardbysn.x-route-id}")String activaterouteid) throws Exception{
        //this.truststorepath = truststorepath;
        //this.truststorepassword = truststorepassword;
        this.tokenbaseUrl = tokenbaseUrl;
        TokenURI = tokenURI;
        this.activateCardbaseUrl = activateCardbaseUrl;
        this.activationURI = activationURI;
        this.tokenUsername = tokenUsername;
        this.tokenPassword = tokenPassword;
        this.tokensourcesystem = tokensourcesystem;
        this.tokensourceidentity = tokensourceidentity;
        ActivationSourcesystem = activationSourcesystem;
        Activaterouteid = activaterouteid;
        this.tokenwebClient = WebClient.builder().
                clientConnector(new ReactorClientHttpConnector(SSLbypasshttpclient.create().baseUrl(tokenbaseUrl))).build();

        this.activatecardwebClient = WebClient.builder().
                clientConnector(new ReactorClientHttpConnector(SSLbypasshttpclient.create()))
                .baseUrl(activateCardbaseUrl)
                .build();
    }

        public Mono<TokenResponsePayload> GenerateTibcoToken()throws Exception {


            return tokenwebClient.
                    post().uri(TokenURI)
                    .header("x-source-system",tokensourcesystem)
                    .header("x-source-identity-token",tokensourceidentity)
                    .header("x-correlation-conversationid", UUID.randomUUID().toString())
                    .bodyValue(new TokenRequestPayload(tokenUsername,tokenPassword))
                    .retrieve()
                    .bodyToMono(TokenResponsePayload.class);
        }
        public Mono<ActivateCardBySNResponsePayload> activatecardbysn(String batch
        ,String tibcotoken)throws Exception{
            Voucheractivationrequestpayload Voucheractivationrequestpayload  =new Voucheractivationrequestpayload(
                    (new VoucherActivationVBMRequest(new VoucherActivationVBO(
                new IDs("27327745",UUID.randomUUID().toString()),
                new Details("BATCH",batch),
                new Parts(new CardDetails(List.of(new Name_Desc("registrationKey","************")))),
                new RelatedParties (List.of(new Name_Desc("reason","Activate new Card")))))));
            ObjectMapper mapper = new ObjectMapper();
            String voucherActivationVBMRequestjson = mapper.writerWithDefaultPrettyPrinter().
                    writeValueAsString(Voucheractivationrequestpayload);

        return activatecardwebClient.post()
                .uri(activationURI)
                .header("Content-Type","application/json")
                .header("Authorization","Bearer "+tibcotoken)
                .header("x-source-system",ActivationSourcesystem)
                .header("x-route-id",Activaterouteid)
                .header("x-correlation-id", UUID.randomUUID().toString())
                .bodyValue(voucherActivationVBMRequestjson)
                .retrieve()
                .bodyToMono(ActivateCardBySNResponsePayload.class);
        }
}
