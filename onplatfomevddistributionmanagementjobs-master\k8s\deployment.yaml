apiVersion: apps/v1
kind: Deployment
metadata:
  name: rms-op-job
  namespace: it-delivery
  labels:
    app: rms-op-job
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rms-op-job
  template:
    metadata:
      labels:
        app: rms-op-job
    spec:
      containers:
      - name: rms-op-job
        image: your-registry/rms-op-job:latest  # Replace with your actual image
        ports:
        - containerPort: 8082
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "oat"
        volumeMounts:
        - name: voucher-files-volume
          mountPath: /voucherfiles
        - name: voucher-keys-volume
          mountPath: /Key
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: voucher-files-volume
        persistentVolumeClaim:
          claimName: voucher-files-pvc
      - name: voucher-keys-volume
        persistentVolumeClaim:
          claimName: voucher-keys-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: rms-op-job-service
  namespace: it-delivery
  labels:
    app: rms-op-job
spec:
  selector:
    app: rms-op-job
  ports:
  - port: 8082
    targetPort: 8082
    protocol: TCP
  type: ClusterIP
