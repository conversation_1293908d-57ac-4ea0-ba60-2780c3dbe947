# Quick Deployment Guide - Corporate Cluster (it-delivery namespace)

## Prerequisites
- Access to corporate Kubernetes cluster
- kubectl configured for your corporate cluster
- Permissions to create resources in `it-delivery` namespace
- Container image available in corporate registry

## Quick Start

### Option 1: Using PowerShell Script (Windows)
```powershell
# Basic deployment with default settings
.\deploy-corporate.ps1

# With custom image registry
.\deploy-corporate.ps1 -ImageRegistry "your-corporate-registry.com/it-delivery" -ImageTag "v1.0.0"

# With custom storage class
.\deploy-corporate.ps1 -StorageClass "fast-ssd"

# Dry run to test configuration
.\deploy-corporate.ps1 -DryRun
```

### Option 2: Using Bash Script (Linux/Mac)
```bash
# Make script executable
chmod +x deploy-corporate.sh

# Basic deployment
./deploy-corporate.sh

# With custom settings
./deploy-corporate.sh --image-registry "your-corporate-registry.com/it-delivery" --image-tag "v1.0.0"

# With storage class
./deploy-corporate.sh --storage-class "fast-ssd"

# Dry run
./deploy-corporate.sh --dry-run
```

### Option 3: Manual kubectl Commands
```bash
# 1. Check cluster access
kubectl get nodes
kubectl get namespace it-delivery

# 2. Check storage classes
kubectl get storageclass

# 3. Deploy PVCs
kubectl apply -f k8s/pvc-voucher-files.yaml

# 4. Check PVC status
kubectl get pvc -n it-delivery

# 5. Update deployment image (edit k8s/deployment.yaml first)
kubectl apply -f k8s/deployment.yaml

# 6. Monitor deployment
kubectl get pods -n it-delivery -w
```

## Before Deployment

### 1. Update Container Image
Edit `k8s/deployment.yaml` and replace:
```yaml
image: your-registry/rms-op-job:latest
```
With your actual corporate registry:
```yaml
image: your-corporate-registry.com/it-delivery/rms-op-job:v1.0.0
```

### 2. Configure Storage Class (if needed)
Edit `k8s/pvc-voucher-files.yaml` and uncomment/set:
```yaml
storageClassName: "your-storage-class"
```

### 3. Check Corporate Requirements
- Image pull secrets
- Security contexts
- Resource quotas
- Network policies

## Post-Deployment

### Verify Deployment
```bash
# Check all resources
kubectl get all -n it-delivery -l app=rms-op-job

# Check PVC status
kubectl get pvc -n it-delivery

# Check pod logs
kubectl logs -l app=rms-op-job -n it-delivery
```

### Upload Files
```bash
# Get pod name
POD_NAME=$(kubectl get pods -n it-delivery -l app=rms-op-job -o jsonpath='{.items[0].metadata.name}')

# Upload voucher files
kubectl cp /local/path/voucher-file.txt $POD_NAME:/voucherfiles/input/ -n it-delivery

# Upload RSA keys
kubectl cp /local/path/rms2048pub.key $POD_NAME:/Key/ -n it-delivery
kubectl cp /local/path/private_key.key $POD_NAME:/Key/ -n it-delivery
```

### Access Application
```bash
# Port forward to access locally
kubectl port-forward svc/rms-op-job-service 8082:8082 -n it-delivery

# Then access: http://localhost:8082
```

## Troubleshooting

### PVC Issues
```bash
kubectl describe pvc voucher-files-pvc -n it-delivery
kubectl get events -n it-delivery --sort-by='.lastTimestamp'
```

### Pod Issues
```bash
kubectl describe pod -l app=rms-op-job -n it-delivery
kubectl logs -l app=rms-op-job -n it-delivery --previous
```

### Image Pull Issues
```bash
kubectl get events -n it-delivery | grep "Failed to pull image"
kubectl get secrets -n it-delivery
```

## Cleanup
```bash
# Remove deployment and service
kubectl delete -f k8s/deployment.yaml

# Remove PVCs (this will delete data!)
kubectl delete -f k8s/pvc-voucher-files.yaml
```

## File Structure
```
k8s/
├── pvc-voucher-files.yaml          # PVCs for storage
├── deployment.yaml                 # Main deployment
├── init-container-setup.yaml       # Alternative with init container
├── local-storage-class.yaml        # Local storage options
├── hostpath-pv.yaml                # HostPath storage (dev only)
└── corporate-deployment-guide.md   # Detailed guide
```
